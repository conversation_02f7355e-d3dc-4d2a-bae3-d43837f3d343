import React from "react";
import { Route, Routes } from "react-router-dom";
import { GoogleOAuthProvider } from '@react-oauth/google';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Pages
import Home from "./pages/Home";
import Doctors from "./pages/Doctors";
import Contact from "./pages/Contact";
import Login from "./pages/Login";
import About from "./pages/About";
import MyProfile from "./pages/MyProfile";
import MyAppointment from "./pages/MyAppointment";
import Appointment from "./pages/Appointment";
import Dashboard from "./pages/Dashboard";

// Components
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import ProtectedRoute from "./components/auth/ProtectedRoute";

// Constants
import { ROUTES, GOOGLE_CONFIG, TOAST_CONFIG } from "./utils/constants";

const App = () => {
  return (
    <GoogleOAuthProvider clientId={GOOGLE_CONFIG.CLIENT_ID}>
      <div className="mx-4 sm:mx-[10%]">
        <Navbar />
        <Routes>
          {/* Public Routes */}
          <Route path={ROUTES.HOME} element={<Home />} />
          <Route path="/doctors" element={<Doctors />} />
          <Route path="/doctors/:speciality" element={<Doctors />} />
          <Route path={ROUTES.ABOUT} element={<About />} />
          <Route path={ROUTES.CONTACT} element={<Contact />} />

          {/* Auth Routes (redirect if already authenticated) */}
          <Route
            path={ROUTES.LOGIN}
            element={
              <ProtectedRoute requireAuth={false}>
                <Login />
              </ProtectedRoute>
            }
          />

          {/* Protected Routes */}
          <Route
            path={ROUTES.DASHBOARD}
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path={ROUTES.MY_PROFILE}
            element={
              <ProtectedRoute>
                <MyProfile />
              </ProtectedRoute>
            }
          />
          <Route
            path={ROUTES.MY_APPOINTMENT}
            element={
              <ProtectedRoute>
                <MyAppointment />
              </ProtectedRoute>
            }
          />
          <Route
            path="/Appointment/:docId"
            element={
              <ProtectedRoute>
                <Appointment />
              </ProtectedRoute>
            }
          />

          {/* Fallback route */}
          <Route path="*" element={<Home />} />
        </Routes>
        <Footer/>

        {/* Toast Notifications */}
        <ToastContainer
          position={TOAST_CONFIG.POSITION}
          autoClose={TOAST_CONFIG.AUTO_CLOSE}
          hideProgressBar={TOAST_CONFIG.HIDE_PROGRESS_BAR}
          newestOnTop={false}
          closeOnClick={TOAST_CONFIG.CLOSE_ON_CLICK}
          rtl={false}
          pauseOnFocusLoss
          draggable={TOAST_CONFIG.DRAGGABLE}
          pauseOnHover={TOAST_CONFIG.PAUSE_ON_HOVER}
          theme="light"
        />
      </div>
    </GoogleOAuthProvider>
  );
};

export default App;
