import React from "react";
import { Route, Routes } from "react-router-dom";

// Pages
import Home from "./pages/Home";
import Doctors from "./pages/Doctors";
import Contact from "./pages/Contact";
import Login from "./pages/Login";
import About from "./pages/About";
import MyProfile from "./pages/MyProfile";
import MyAppointment from "./pages/MyAppointment";
import Appointment from "./pages/Appointment";
import Dashboard from "./pages/Dashboard";
import VerifyEmail from "./pages/VerifyEmail";

// Components
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";

const App = () => {
  return (
    <div className="mx-4 sm:mx-[10%]">
      <Navbar />
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<Home />} />
        <Route path="/doctors" element={<Doctors />} />
        <Route path="/doctors/:speciality" element={<Doctors />} />
        <Route path="/about" element={<About />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/login" element={<Login />} />

        {/* Authentication Routes */}
        <Route path="/verify-email" element={<VerifyEmail />} />

        {/* Dashboard Route */}
        <Route path="/dashboard" element={<Dashboard />} />

        {/* Protected Routes - Temporarily without protection */}
        <Route path="/My-Profile" element={<MyProfile />} />
        <Route path="/My-Appointment" element={<MyAppointment />} />
        <Route path="/Appointment/:docId" element={<Appointment />} />

        {/* Fallback route */}
        <Route path="*" element={<Home />} />
      </Routes>
      <Footer/>
    </div>
  );
};

export default App;
