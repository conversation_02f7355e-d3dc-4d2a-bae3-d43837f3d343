import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AppContext';
import { ROUTES } from '../../utils/constants';
import LoadingSpinner from '../common/LoadingSpinner';

const ProtectedRoute = ({ children, requireAuth = true, redirectTo = ROUTES.LOGIN }) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    // Save the attempted location for redirecting after login
    return (
      <Navigate 
        to={redirectTo} 
        state={{ from: location }} 
        replace 
      />
    );
  }

  // If authentication is not required but user is authenticated
  // (e.g., login page when already logged in)
  if (!requireAuth && isAuthenticated) {
    // Redirect to dashboard or the intended destination
    const from = location.state?.from?.pathname || ROUTES.DASHBOARD;
    return <Navigate to={from} replace />;
  }

  // Render the protected component
  return children;
};

// Higher-order component for role-based access
export const withRoleProtection = (allowedRoles = []) => {
  return (WrappedComponent) => {
    return (props) => {
      const { user, isAuthenticated, isLoading } = useAuth();

      if (isLoading) {
        return <LoadingSpinner />;
      }

      if (!isAuthenticated) {
        return <Navigate to={ROUTES.LOGIN} replace />;
      }

      if (allowedRoles.length > 0 && !allowedRoles.includes(user?.role)) {
        return (
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6 text-center">
              <div className="mb-4">
                <svg
                  className="mx-auto h-12 w-12 text-red-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Access Denied
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                You don't have permission to access this page.
              </p>
              <button
                onClick={() => window.history.back()}
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
              >
                Go Back
              </button>
            </div>
          </div>
        );
      }

      return <WrappedComponent {...props} />;
    };
  };
};

// Specific role-based route components
export const AdminRoute = ({ children }) => {
  const AdminProtectedComponent = withRoleProtection(['Admin'])(() => children);
  return <AdminProtectedComponent />;
};

export const DoctorRoute = ({ children }) => {
  const DoctorProtectedComponent = withRoleProtection(['Doctor', 'Admin'])(() => children);
  return <DoctorProtectedComponent />;
};

export const PatientRoute = ({ children }) => {
  const PatientProtectedComponent = withRoleProtection(['Patient', 'Admin'])(() => children);
  return <PatientProtectedComponent />;
};

// Email verification protection
export const EmailVerifiedRoute = ({ children }) => {
  const { user, isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to={ROUTES.LOGIN} replace />;
  }

  if (!user?.isEmailConfirmed) {
    return <Navigate to={ROUTES.VERIFY_EMAIL} replace />;
  }

  return children;
};

// Two-factor authentication protection
export const TwoFactorRoute = ({ children }) => {
  const { user, isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to={ROUTES.LOGIN} replace />;
  }

  // If 2FA is enabled but not verified in this session
  if (user?.twoFactorEnabled && !user?.twoFactorVerified) {
    return <Navigate to={ROUTES.TWO_FACTOR_LOGIN} replace />;
  }

  return children;
};

// Combined protection (authentication + email verification + 2FA)
export const FullyProtectedRoute = ({ children, allowedRoles = [] }) => {
  return (
    <ProtectedRoute>
      <EmailVerifiedRoute>
        <TwoFactorRoute>
          {allowedRoles.length > 0 ? (
            React.createElement(
              withRoleProtection(allowedRoles)(() => children)
            )
          ) : (
            children
          )}
        </TwoFactorRoute>
      </EmailVerifiedRoute>
    </ProtectedRoute>
  );
};

export default ProtectedRoute;
