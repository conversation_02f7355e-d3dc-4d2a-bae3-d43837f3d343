// Simple test to verify our setup
console.log('Testing PRESCRIPTO setup...');

// Test if we can import our constants
try {
  const constants = require('./src/utils/constants.js');
  console.log('✅ Constants imported successfully');
  console.log('API Base URL:', constants.API_CONFIG.BASE_URL);
} catch (error) {
  console.log('❌ Error importing constants:', error.message);
}

// Test if package.json has our dependencies
try {
  const packageJson = require('./package.json');
  const requiredDeps = [
    '@react-oauth/google',
    'js-cookie',
    'jwt-decode',
    'qrcode.react',
    'react-hook-form'
  ];
  
  console.log('\nChecking dependencies:');
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep}: Missing`);
    }
  });
  
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
}

console.log('\nSetup test completed!');
