{"name": "final-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"@tailwindcss/cli": "^4.0.9", "@tailwindcss/postcss": "^4.0.9", "@tailwindcss/vite": "^4.0.9", "axios": "^1.8.1", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.2.0", "react-toastify": "^11.0.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "gh-pages": "^6.3.0", "globals": "^15.15.0", "tailwindcss": "^4.0.9", "vite": "^6.2.0"}}