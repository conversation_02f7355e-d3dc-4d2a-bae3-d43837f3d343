<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DotnetAuth</name>
    </assembly>
    <members>
        <member name="T:DotnetAuth.Controllers.AuthController">
            <summary>
            Authentication and User Management Controller
            </summary>
            <remarks>
            This controller handles all authentication-related operations including:
            - User registration and email verification
            - User login and logout
            - Password management (forgot/reset)
            - Account management (email/phone updates)
            - User profile operations
            - Activity tracking and login history
            </remarks>
        </member>
        <member name="M:DotnetAuth.Controllers.AuthController.Register(DotnetAuth.Domain.Contracts.UserRegisterRequest)">
             <summary>
             Register a new user account
             </summary>
             <remarks>
             Creates a new user account with the provided information. The user will receive an OTP via email for verification.
            
             **Registration Process:**
             1. Validates the registration data and CAPTCHA
             2. Creates the user account with unverified status
             3. Generates and sends an OTP to the provided email
             4. Assigns a default profile picture
             5. Adds the user to the specified role
            
             **Password Requirements:**
             - Minimum 12 characters
             - At least one uppercase letter
             - At least one lowercase letter
             - At least one digit
             - At least one special character
             - At least 4 unique characters
            
             **Valid Roles:** Doctor, Patient (Admin role cannot be registered via this endpoint)
             </remarks>
             <param name="request">User registration data including personal information, credentials, and CAPTCHA token</param>
             <returns>Registration result with success status and OTP for email verification</returns>
             <response code="200">Registration successful - OTP sent to email</response>
             <response code="400">Invalid registration data, weak password, or CAPTCHA verification failed</response>
             <response code="409">User with this email already exists</response>
             <response code="500">Internal server error during registration process</response>
        </member>
        <member name="M:DotnetAuth.Controllers.AuthController.Login(DotnetAuth.Domain.Contracts.UserLoginRequest)">
             <summary>
             Authenticate user and generate access tokens
             </summary>
             <remarks>
             Authenticates a user with email and password, returning JWT tokens for API access.
            
             **Login Process:**
             1. Validates email and password credentials
             2. Verifies CAPTCHA token
             3. Checks if account is verified and not locked
             4. Generates JWT access token and refresh token
             5. Logs the login activity
            
             **Two-Factor Authentication:**
             If 2FA is enabled, this endpoint will return `RequiresTwoFactor: true` and you must use the `/api/two-factor-login` endpoint to complete authentication.
            
             **Remember Me:**
             - When enabled: Refresh token valid for 30 days
             - When disabled: Refresh token valid for 1 day
            
             **Account Lockout:**
             After 5 failed attempts, the account will be locked for 30 minutes.
             </remarks>
             <param name="request">Login credentials including email, password, CAPTCHA token, and remember me option</param>
             <returns>User information with JWT tokens or two-factor authentication requirement</returns>
             <response code="200">Login successful - returns user data and tokens</response>
             <response code="400">Invalid credentials, unverified account, or CAPTCHA verification failed</response>
             <response code="401">Authentication failed - invalid email or password</response>
             <response code="423">Account is locked due to too many failed attempts</response>
             <response code="500">Internal server error during authentication</response>
        </member>
        <member name="M:DotnetAuth.Controllers.AuthController.VerifyOtp(DotnetAuth.Domain.Contracts.VerifyOtpRequest)">
             <summary>
             Verify email address using OTP
             </summary>
             <remarks>
             Verifies the user's email address using the OTP sent during registration.
            
             **Verification Process:**
             1. Validates the provided OTP against the stored value
             2. Checks if the OTP has not expired (15-minute validity)
             3. Marks the email as verified
             4. Generates JWT access token for the verified user
             5. Logs the verification activity
            
             **OTP Expiry:**
             OTPs are valid for 15 minutes from the time they are generated.
            
             **After Verification:**
             The user account becomes fully active and can be used for login.
             </remarks>
             <param name="request">Email and OTP for verification</param>
             <returns>Verification result with access token if successful</returns>
             <response code="200">Email verified successfully - returns access token</response>
             <response code="400">Invalid or expired OTP</response>
             <response code="404">User not found or already verified</response>
             <response code="500">Internal server error during verification</response>
        </member>
        <member name="M:DotnetAuth.Controllers.AuthController.GetCurrentUser">
             <summary>
             Get current authenticated user's profile information
             </summary>
             <remarks>
             Retrieves the complete profile information for the currently authenticated user.
            
             **Returned Information:**
             - User ID and basic profile data (name, email, gender)
             - Account status (email verification, creation/update dates)
             - User role (Admin, Doctor, Patient)
             - Current access token
            
             **Authentication Required:**
             This endpoint requires a valid JWT token in the Authorization header.
            
             **Activity Logging:**
             This action is logged as "Profile View" in the user's activity history.
             </remarks>
             <returns>Current user's profile information</returns>
             <response code="200">User profile retrieved successfully</response>
             <response code="401">Unauthorized - invalid or expired token</response>
             <response code="404">User not found</response>
             <response code="500">Internal server error</response>
        </member>
        <member name="M:DotnetAuth.Controllers.AuthController.Logout">
             <summary>
             Logout user and invalidate access token
             </summary>
             <remarks>
             Logs out the current user by adding their JWT token to the blacklist, preventing further use.
            
             **Logout Process:**
             1. Extracts the JWT token from the Authorization header
             2. Adds the token to the blacklist with its expiry time
             3. Logs the logout activity
             4. Returns success confirmation
            
             **Token Blacklisting:**
             Once blacklisted, the token cannot be used for any authenticated requests until it naturally expires.
            
             **Security Note:**
             Always call this endpoint when users log out to ensure their tokens are immediately invalidated.
             </remarks>
             <returns>Logout confirmation message</returns>
             <response code="200">Logout successful - token invalidated</response>
             <response code="400">No token provided or invalid token format</response>
             <response code="401">Unauthorized - invalid or expired token</response>
             <response code="500">Internal server error during logout</response>
        </member>
        <member name="T:DotnetAuth.Domain.Contracts.ErrorResponse">
            <summary>
            Standard error response model
            </summary>
            <remarks>
            Used to return consistent error information across all API endpoints.
            </remarks>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.ErrorResponse.Titel">
            <summary>
            Error title or category
            </summary>
            <example>Authentication Error</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.ErrorResponse.StatusCode">
            <summary>
            HTTP status code
            </summary>
            <example>400</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.ErrorResponse.Message">
            <summary>
            Error message describing what went wrong
            </summary>
            <example>Invalid email or password</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.ErrorResponse.ErrorCode">
            <summary>
            Optional error code for programmatic handling
            </summary>
            <example>AUTH_001</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.ErrorResponse.Details">
            <summary>
            Additional error details (used in development)
            </summary>
            <example>Stack trace or detailed error information</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.ErrorResponse.Timestamp">
            <summary>
            Timestamp when the error occurred
            </summary>
            <example>2024-01-15T10:30:00Z</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.ErrorResponse.RequestId">
            <summary>
            Request ID for tracking purposes
            </summary>
            <example>req_12345</example>
        </member>
        <member name="T:DotnetAuth.Domain.Contracts.ValidationErrorResponse">
            <summary>
            Validation error response with field-specific errors
            </summary>
            <remarks>
            Extended error response that includes validation errors for specific fields.
            </remarks>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.ValidationErrorResponse.ValidationErrors">
            <summary>
            Dictionary of field validation errors
            </summary>
            <example>{"Email": ["Email is required"], "Password": ["Password must be at least 12 characters"]}</example>
        </member>
        <member name="T:DotnetAuth.Domain.Contracts.UserRole">
            <summary>
            User roles available in the system
            </summary>
        </member>
        <member name="F:DotnetAuth.Domain.Contracts.UserRole.Admin">
            <summary>
            Administrator with full system access
            </summary>
        </member>
        <member name="F:DotnetAuth.Domain.Contracts.UserRole.Doctor">
            <summary>
            Medical doctor with healthcare provider privileges
            </summary>
        </member>
        <member name="F:DotnetAuth.Domain.Contracts.UserRole.Patient">
            <summary>
            Patient with basic user privileges
            </summary>
        </member>
        <member name="T:DotnetAuth.Domain.Contracts.Gender">
            <summary>
            Gender options for user profiles
            </summary>
        </member>
        <member name="F:DotnetAuth.Domain.Contracts.Gender.Male">
            <summary>
            Male gender
            </summary>
        </member>
        <member name="F:DotnetAuth.Domain.Contracts.Gender.Female">
            <summary>
            Female gender
            </summary>
        </member>
        <member name="T:DotnetAuth.Domain.Contracts.UserRegisterRequest">
            <summary>
            User registration request model
            </summary>
            <remarks>
            Contains all required information for creating a new user account.
            Password must meet security requirements and CAPTCHA verification is required.
            </remarks>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.UserRegisterRequest.FirstName">
            <summary>
            User's first name
            </summary>
            <example>John</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.UserRegisterRequest.LastName">
            <summary>
            User's last name
            </summary>
            <example>Doe</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.UserRegisterRequest.Email">
            <summary>
            User's email address (will be used as username)
            </summary>
            <example><EMAIL></example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.UserRegisterRequest.Password">
            <summary>
            User's password (must meet security requirements)
            </summary>
            <example>MySecureP@ssw0rd123</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.UserRegisterRequest.Gender">
            <summary>
            User's gender
            </summary>
            <example>Male</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.UserRegisterRequest.Role">
            <summary>
            User's role in the system
            </summary>
            <example>Patient</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.UserRegisterRequest.CaptchaToken">
            <summary>
            CAPTCHA verification token
            </summary>
            <example>captcha_token_12345</example>
        </member>
        <member name="T:DotnetAuth.Domain.Contracts.UserLoginRequest">
            <summary>
            User login request model
            </summary>
            <remarks>
            Contains credentials and options for user authentication.
            CAPTCHA verification is required to prevent brute force attacks.
            </remarks>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.UserLoginRequest.Email">
            <summary>
            User's email address
            </summary>
            <example><EMAIL></example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.UserLoginRequest.Password">
            <summary>
            User's password
            </summary>
            <example>MySecureP@ssw0rd123</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.UserLoginRequest.RememberMe">
            <summary>
            Whether to extend the session duration
            </summary>
            <example>false</example>
        </member>
        <member name="P:DotnetAuth.Domain.Contracts.UserLoginRequest.CaptchaToken">
            <summary>
            CAPTCHA verification token
            </summary>
            <example>captcha_token_67890</example>
        </member>
        <member name="T:DotnetAuth.Migrations.Initial">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.Initial.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.Initial.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.Initial.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
        <member name="T:DotnetAuth.Migrations.FullnamePROPadd">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.FullnamePROPadd.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.FullnamePROPadd.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.FullnamePROPadd.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
        <member name="T:DotnetAuth.Migrations.AddLoginHistoryAndAccountActivity">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddLoginHistoryAndAccountActivity.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddLoginHistoryAndAccountActivity.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddLoginHistoryAndAccountActivity.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
        <member name="T:DotnetAuth.Migrations.AddProfilePictureTables">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddProfilePictureTables.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddProfilePictureTables.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddProfilePictureTables.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
        <member name="T:DotnetAuth.Migrations.AddTwoFactorAuthentication">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddTwoFactorAuthentication.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddTwoFactorAuthentication.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddTwoFactorAuthentication.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
        <member name="T:DotnetAuth.Migrations.AddExternalAuthenticationFields">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddExternalAuthenticationFields.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddExternalAuthenticationFields.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:DotnetAuth.Migrations.AddExternalAuthenticationFields.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
        <member name="T:DotnetAuth.Service.ToekenServiceImple">
            <summary>
            Implementation of the ITokenService interface for generating JWT tokens and refresh tokens.
            </summary>
        </member>
        <member name="M:DotnetAuth.Service.ToekenServiceImple.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Microsoft.AspNetCore.Identity.UserManager{DotnetAuth.Domain.Entities.ApplicationUser},Microsoft.Extensions.Logging.ILogger{DotnetAuth.Service.ToekenServiceImple},DotnetAuth.Service.ITokenBlacklistService)">
            <summary>
            Initializes a new instance of the <see cref="T:DotnetAuth.Service.ToekenServiceImple"/> class.
            </summary>
            <param name="configuration">The configuration settings.</param>
            <param name="userManager">The user manager for managing user information.</param>
            <param name="logger">The logger for logging information.</param>
            <param name="tokenBlacklistService">The token blacklist service for managing token revocation.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when JWT secret key is not configured.</exception>
        </member>
        <member name="M:DotnetAuth.Service.ToekenServiceImple.GenerateToken(DotnetAuth.Domain.Entities.ApplicationUser)">
            <summary>
            Generates a JWT token for the specified user.
            </summary>
            <param name="user">The user for whom the token is generated.</param>
            <returns>A task that represents the asynchronous operation. The task result contains the generated JWT token.</returns>
        </member>
        <member name="M:DotnetAuth.Service.ToekenServiceImple.GetClaimsAsync(DotnetAuth.Domain.Entities.ApplicationUser)">
            <summary>
            Gets the claims for the specified user.
            </summary>
            <param name="user">The user for whom the claims are retrieved.</param>
            <returns>A task that represents the asynchronous operation. The task result contains the list of claims.</returns>
        </member>
        <member name="M:DotnetAuth.Service.ToekenServiceImple.GenerateTokenOptions(Microsoft.IdentityModel.Tokens.SigningCredentials,System.Collections.Generic.List{System.Security.Claims.Claim})">
            <summary>
            Generates the token options for the JWT token.
            </summary>
            <param name="signingCredentials">The signing credentials for the token.</param>
            <param name="claims">The claims to be included in the token.</param>
            <returns>The generated JWT token options.</returns>
        </member>
        <member name="M:DotnetAuth.Service.ToekenServiceImple.GenerateRefreshToken">
            <summary>
            Generates a refresh token.
            </summary>
            <returns>The generated refresh token.</returns>
        </member>
        <member name="M:DotnetAuth.Service.ToekenServiceImple.RevokeTokenAsync(System.String)">
            <summary>
            Revokes a token by adding it to the blacklist.
            </summary>
            <param name="token">The token to be revoked.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:DotnetAuth.Service.ToekenServiceImple.GetTokenExpiryTimeAsync(System.String)">
            <summary>
            Gets the expiry time of a JWT token.
            </summary>
            <param name="token">The token for which the expiry time is retrieved.</param>
            <returns>A task that represents the asynchronous operation. The task result contains the expiry time of the token.</returns>
        </member>
        <member name="T:DotnetAuth.Service.UserServiceImpl">
            <summary>
            Implementation of the IUserServices interface for managing user-related operations.
            </summary>
        </member>
        <member name="M:DotnetAuth.Service.UserServiceImpl.#ctor(DotnetAuth.Service.ITokenService,DotnetAuth.Service.ICurrentUserService,Microsoft.AspNetCore.Identity.UserManager{DotnetAuth.Domain.Entities.ApplicationUser},AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{DotnetAuth.Service.UserServiceImpl},DotnetAuth.Service.IEmailService,DotnetAuth.Infrastructure.Context.ApplicationDbContext,System.IServiceProvider,Microsoft.AspNetCore.Http.IHttpContextAccessor,DotnetAuth.Service.IActivityLoggingService,DotnetAuth.Service.ICaptchaService)">
            <summary>
            Initializes a new instance of the <see cref="T:DotnetAuth.Service.UserServiceImpl"/> class.
            </summary>
            <param name="tokenService">The token service for generating tokens.</param>
            <param name="currentUserService">The current user service for retrieving current user information.</param>
            <param name="userManager">The user manager for managing user information.</param>
            <param name="mapper">The mapper for mapping objects.</param>
            <param name="logger">The logger for logging information.</param>
            <param name="emailService">The email service for sending emails.</param>
            <param name="context">The application database context.</param>
            <param name="serviceProvider">The service provider for resolving services.</param>
            <param name="httpContextAccessor">The HTTP context accessor for accessing HTTP context.</param>
        </member>
        <member name="M:DotnetAuth.Service.UserServiceImpl.RegisterAsync(DotnetAuth.Domain.Contracts.UserRegisterRequest)">
            <summary>
            Registers a new user.
            </summary>
            <param name="request">The user registration request.</param>
            <returns>A task that represents the asynchronous operation. The task result contains the user response.</returns>
            <exception cref="T:System.Exception">Thrown when the email already exists or user creation fails.</exception>
        </member>
        <member name="M:DotnetAuth.Service.UserServiceImpl.GenerateUserName(System.String,System.String)">
            <summary>
            Generates a unique username by concatenating the first name and last name.
            </summary>
            <param name="firstName">The first name of the user.</param>
            <param name="lastName">The last name of the user.</param>
            <returns>The generated unique username.</returns>
        </member>
        <member name="M:DotnetAuth.Service.UserServiceImpl.LoginAsync(DotnetAuth.Domain.Contracts.UserLoginRequest)">
            <summary>
            Logs in a user.
            </summary>
            <param name="request">The user login request.</param>
            <returns>A task that represents the asynchronous operation. The task result contains the login response.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when the login request is null.</exception>
            <exception cref="T:System.Exception">Thrown when the email or password is invalid or user update fails.</exception>
        </member>
        <member name="M:DotnetAuth.Service.UserServiceImpl.GetByIdAsync(System.Guid)">
            <summary>
            Gets a user by ID.
            </summary>
            <param name="id">The ID of the user.</param>
            <returns>A task that represents the asynchronous operation. The task result contains the user response.</returns>
            <exception cref="T:System.Exception">Thrown when the user is not found.</exception>
        </member>
        <member name="M:DotnetAuth.Service.UserServiceImpl.GetCurrentUserAsync">
            <summary>
            Gets the current user.
            </summary>
            <returns>A task that represents the asynchronous operation. The task result contains the current user response.</returns>
            <exception cref="T:System.Exception">Thrown when the user is not found.</exception>
        </member>
        <member name="M:DotnetAuth.Service.UserServiceImpl.RefreshTokenAsync(DotnetAuth.Domain.Contracts.RefreshTokenRequest)">
            <summary>
            Refreshes the access token using the refresh token.
            </summary>
            <param name="request">The refresh token request.</param>
            <returns>A task that represents the asynchronous operation. The task result contains the current user response.</returns>
            <exception cref="T:System.Exception">Thrown when the refresh token is invalid or expired.</exception>
        </member>
        <member name="M:DotnetAuth.Service.UserServiceImpl.RevokeRefreshToken(DotnetAuth.Domain.Contracts.RefreshTokenRequest)">
            <summary>
            Revokes the refresh token.
            </summary>
            <param name="refreshTokenRemoveRequest">The refresh token request to be revoked.</param>
            <returns>A task that represents the asynchronous operation. The task result contains the revoke refresh token response.</returns>
            <exception cref="T:System.Exception">Thrown when the refresh token is invalid or expired.</exception>
        </member>
        <member name="M:DotnetAuth.Service.UserServiceImpl.UpdateAsync(System.Guid,DotnetAuth.Domain.Contracts.UpdateUserRequest)">
            <summary>
            Updates a user.
            </summary>
            <param name="id">The ID of the user to be updated.</param>
            <param name="request">The update user request.</param>
            <returns>A task that represents the asynchronous operation. The task result contains the user response.</returns>
            <exception cref="T:System.Exception">Thrown when the user is not found.</exception>
        </member>
        <member name="M:DotnetAuth.Service.UserServiceImpl.DeleteAsync(System.Guid)">
            <summary>
            Deletes a user.
            </summary>
            <param name="id">The ID of the user to be deleted.</param>
            <returns>A task that represents the asynchronous operation.</returns>
            <exception cref="T:System.Exception">Thrown when the user is not found.</exception>
        </member>
    </members>
</doc>
