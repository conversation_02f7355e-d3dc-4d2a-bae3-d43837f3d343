import React, { useState } from 'react';
import { GoogleLogin } from '@react-oauth/google';
import { useAuth } from '../../context/AppContext';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '../../utils/constants';
import { toast } from 'react-toastify';

const GoogleSignIn = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { googleAuth } = useAuth();
  const navigate = useNavigate();

  const handleGoogleSuccess = async (credentialResponse) => {
    setIsLoading(true);
    
    try {
      const result = await googleAuth(credentialResponse.credential);
      
      if (result.success) {
        if (result.requiresRegistration) {
          // Navigate to Google registration completion
          navigate(ROUTES.REGISTER, {
            state: {
              googleData: result.data,
              isGoogleRegistration: true,
            }
          });
        }
        // If successful login, navigation is handled by the auth context
      }
    } catch (error) {
      console.error('Google sign-in error:', error);
      toast.error('Google sign-in failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleError = () => {
    console.error('Google sign-in failed');
    toast.error('Google sign-in failed. Please try again.');
  };

  return (
    <div className="w-full">
      <GoogleLogin
        onSuccess={handleGoogleSuccess}
        onError={handleGoogleError}
        useOneTap={false}
        theme="outline"
        size="large"
        width="100%"
        text="signin_with"
        shape="rectangular"
        logo_alignment="left"
        disabled={isLoading}
      />
      
      {isLoading && (
        <div className="mt-2 text-center">
          <p className="text-sm text-gray-500">Signing in with Google...</p>
        </div>
      )}
    </div>
  );
};

export default GoogleSignIn;
