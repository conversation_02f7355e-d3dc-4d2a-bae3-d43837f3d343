import apiService from './api';
import { API_ENDPOINTS, USER_ROLES, GENDER_OPTIONS } from '../utils/constants';

class AuthService {
  // User Registration (exact API format)
  async register(userData) {
    try {
      const registerData = {
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        password: userData.password,
        gender: userData.gender, // Must be 'Male' or 'Female'
        role: userData.role, // Must be 'Doctor' or 'Patient'
        captchaToken: userData.captchaToken || 'temp_captcha_token', // Required by API
      };

      const response = await apiService.post(API_ENDPOINTS.REGISTER, registerData);
      return {
        success: response.success || true,
        data: response,
        message: response.message || 'Registration successful. Please verify your email.',
        otp: response.otp, // OTP for development/testing
        userId: response.userId,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.response?.data?.titel || 'Registration failed',
        errorCode: error.response?.data?.errorCode,
      };
    }
  }

  // User Login (exact API format)
  async login(credentials) {
    try {
      const loginData = {
        email: credentials.email,
        password: credentials.password,
        rememberMe: credentials.rememberMe || false,
        captchaToken: credentials.captchaToken || 'temp_captcha_token', // Required by API
      };

      const response = await apiService.post(API_ENDPOINTS.LOGIN, loginData);

      // Store tokens if login successful
      if (response.accessToken && response.refreshToken) {
        apiService.setTokens(
          response.accessToken,
          response.refreshToken,
          credentials.rememberMe
        );
      }

      return {
        success: true,
        data: response,
        requiresTwoFactor: response.requiresTwoFactor || false,
        message: 'Login successful',
      };
    } catch (error) {
      // Handle specific error cases
      const errorData = error.response?.data;
      let errorMessage = 'Login failed';

      if (errorData?.errorCode === 'AUTH_002') {
        errorMessage = 'Account not verified. Please check your email for verification instructions.';
      } else if (errorData?.errorCode === 'AUTH_003') {
        errorMessage = 'Account is locked due to too many failed attempts. Please try again later.';
      } else if (errorData?.message || errorData?.titel) {
        errorMessage = errorData.message || errorData.titel;
      }

      return {
        success: false,
        error: errorMessage,
        errorCode: errorData?.errorCode,
        requiresVerification: errorData?.errorCode === 'AUTH_002',
      };
    }
  }

  // Two-Factor Login
  async twoFactorLogin(twoFactorData) {
    try {
      const response = await apiService.post(API_ENDPOINTS.TWO_FACTOR_LOGIN, {
        email: twoFactorData.email,
        twoFactorCode: twoFactorData.code,
        rememberMe: twoFactorData.rememberMe || false,
      });

      // Store tokens
      if (response.accessToken && response.refreshToken) {
        apiService.setTokens(
          response.accessToken,
          response.refreshToken,
          twoFactorData.rememberMe
        );
      }

      return {
        success: true,
        data: response,
        message: 'Two-factor authentication successful',
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Two-factor authentication failed',
      };
    }
  }

  // Email Verification
  async verifyEmail(verificationData) {
    try {
      const response = await apiService.post(API_ENDPOINTS.VERIFY_OTP, {
        email: verificationData.email,
        otp: verificationData.otp,
      });

      return {
        success: true,
        data: response,
        message: 'Email verified successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Email verification failed',
      };
    }
  }

  // Forgot Password
  async forgotPassword(email) {
    try {
      const response = await apiService.post(API_ENDPOINTS.FORGOT_PASSWORD, {
        email,
      });

      return {
        success: true,
        data: response,
        message: 'Password reset email sent',
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to send password reset email',
      };
    }
  }

  // Reset Password
  async resetPassword(resetData) {
    try {
      const response = await apiService.post(API_ENDPOINTS.RESET_PASSWORD, {
        email: resetData.email,
        token: resetData.token,
        newPassword: resetData.newPassword,
      });

      return {
        success: true,
        data: response,
        message: 'Password reset successful',
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Password reset failed',
      };
    }
  }

  // User Logout
  async logout() {
    try {
      // Call logout endpoint if authenticated
      if (apiService.isAuthenticated()) {
        await apiService.post(API_ENDPOINTS.LOGOUT);
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      // Always clear tokens locally
      apiService.clearTokens();
      
      // Dispatch logout event
      window.dispatchEvent(new CustomEvent('auth:logout'));
      
      return {
        success: true,
        message: 'Logged out successfully',
      };
    }
  }

  // Google Authentication
  async googleAuth(idToken) {
    try {
      const response = await apiService.post(API_ENDPOINTS.GOOGLE_AUTH, {
        idToken,
      });

      if (response.success && !response.requiresRegistration) {
        // Store tokens for existing user
        if (response.accessToken && response.refreshToken) {
          apiService.setTokens(response.accessToken, response.refreshToken);
        }
      }

      return {
        success: true,
        data: response,
        requiresRegistration: response.requiresRegistration || false,
        message: response.requiresRegistration 
          ? 'Please complete your registration' 
          : 'Google authentication successful',
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Google authentication failed',
      };
    }
  }

  // Google Registration (for new users)
  async googleRegister(registrationData) {
    try {
      const response = await apiService.post(API_ENDPOINTS.GOOGLE_REGISTER, {
        idToken: registrationData.idToken,
        role: registrationData.role || USER_ROLES.PATIENT,
        gender: registrationData.gender || GENDER_OPTIONS.MALE,
        provider: 'Google',
      });

      // Store tokens
      if (response.accessToken && response.refreshToken) {
        apiService.setTokens(response.accessToken, response.refreshToken);
      }

      return {
        success: true,
        data: response,
        message: 'Google registration successful',
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Google registration failed',
      };
    }
  }

  // Get Current User
  async getCurrentUser() {
    try {
      const response = await apiService.get(API_ENDPOINTS.GET_CURRENT_USER);
      return {
        success: true,
        data: response,
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to get user data',
      };
    }
  }

  // Check Authentication Status
  isAuthenticated() {
    return apiService.isAuthenticated();
  }

  // Get User from Token
  getUserFromToken() {
    return apiService.getCurrentUser();
  }

  // Change Email
  async changeEmail(emailData) {
    try {
      const response = await apiService.post(API_ENDPOINTS.CHANGE_EMAIL, {
        newEmail: emailData.newEmail,
        password: emailData.password,
      });

      return {
        success: true,
        data: response,
        message: 'Email change initiated. Please verify your new email.',
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to change email',
      };
    }
  }

  // Update Phone Number
  async updatePhone(phoneData) {
    try {
      const response = await apiService.post(API_ENDPOINTS.UPDATE_PHONE, {
        phoneNumber: phoneData.phoneNumber,
      });

      return {
        success: true,
        data: response,
        message: 'Phone number updated. Please verify your phone.',
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update phone number',
      };
    }
  }

  // Verify Phone Number
  async verifyPhone(verificationData) {
    try {
      const response = await apiService.post(API_ENDPOINTS.VERIFY_PHONE, {
        phoneNumber: verificationData.phoneNumber,
        verificationCode: verificationData.code,
      });

      return {
        success: true,
        data: response,
        message: 'Phone number verified successfully',
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Phone verification failed',
      };
    }
  }
}

// Create and export a singleton instance
const authService = new AuthService();
export default authService;
