{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "profile-pictures/defaults/default-picture-profile.d90hbt9f7m.png", "AssetFile": "profile-pictures/defaults/default-picture-profile.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5153"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 19:17:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d90hbt9f7m"}, {"Name": "integrity", "Value": "sha256-keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q="}, {"Name": "label", "Value": "profile-pictures/defaults/default-picture-profile.png"}]}, {"Route": "profile-pictures/defaults/default-picture-profile.png", "AssetFile": "profile-pictures/defaults/default-picture-profile.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5153"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 19:17:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q="}]}]}