import { createRoot } from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import "./index.css";
import App from "./App.jsx";
import AppContextProvider from "./context/AppContext.jsx";
import React from "react";

createRoot(document.getElementById("root")).render(
  <BrowserRouter>
    <AppContextProvider>
      <App />
    </AppContextProvider>
  </BrowserRouter>,
);
