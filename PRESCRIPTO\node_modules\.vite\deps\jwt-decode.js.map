{"version": 3, "sources": ["../../jwt-decode/build/esm/index.js"], "sourcesContent": ["export class InvalidTokenError extends Error {\n}\nInvalidTokenError.prototype.name = \"InvalidTokenError\";\nfunction b64DecodeUnicode(str) {\n    return decodeURIComponent(atob(str).replace(/(.)/g, (m, p) => {\n        let code = p.charCodeAt(0).toString(16).toUpperCase();\n        if (code.length < 2) {\n            code = \"0\" + code;\n        }\n        return \"%\" + code;\n    }));\n}\nfunction base64UrlDecode(str) {\n    let output = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n    switch (output.length % 4) {\n        case 0:\n            break;\n        case 2:\n            output += \"==\";\n            break;\n        case 3:\n            output += \"=\";\n            break;\n        default:\n            throw new Error(\"base64 string is not of the correct length\");\n    }\n    try {\n        return b64DecodeUnicode(output);\n    }\n    catch (err) {\n        return atob(output);\n    }\n}\nexport function jwtDecode(token, options) {\n    if (typeof token !== \"string\") {\n        throw new InvalidTokenError(\"Invalid token specified: must be a string\");\n    }\n    options || (options = {});\n    const pos = options.header === true ? 0 : 1;\n    const part = token.split(\".\")[pos];\n    if (typeof part !== \"string\") {\n        throw new InvalidTokenError(`Invalid token specified: missing part #${pos + 1}`);\n    }\n    let decoded;\n    try {\n        decoded = base64UrlDecode(part);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid base64 for part #${pos + 1} (${e.message})`);\n    }\n    try {\n        return JSON.parse(decoded);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid json for part #${pos + 1} (${e.message})`);\n    }\n}\n"], "mappings": ";;;AAAO,IAAM,oBAAN,cAAgC,MAAM;AAC7C;AACA,kBAAkB,UAAU,OAAO;AACnC,SAAS,iBAAiB,KAAK;AAC3B,SAAO,mBAAmB,KAAK,GAAG,EAAE,QAAQ,QAAQ,CAAC,GAAG,MAAM;AAC1D,QAAI,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AACpD,QAAI,KAAK,SAAS,GAAG;AACjB,aAAO,MAAM;AAAA,IACjB;AACA,WAAO,MAAM;AAAA,EACjB,CAAC,CAAC;AACN;AACA,SAAS,gBAAgB,KAAK;AAC1B,MAAI,SAAS,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACrD,UAAQ,OAAO,SAAS,GAAG;AAAA,IACvB,KAAK;AACD;AAAA,IACJ,KAAK;AACD,gBAAU;AACV;AAAA,IACJ,KAAK;AACD,gBAAU;AACV;AAAA,IACJ;AACI,YAAM,IAAI,MAAM,4CAA4C;AAAA,EACpE;AACA,MAAI;AACA,WAAO,iBAAiB,MAAM;AAAA,EAClC,SACO,KAAK;AACR,WAAO,KAAK,MAAM;AAAA,EACtB;AACJ;AACO,SAAS,UAAU,OAAO,SAAS;AACtC,MAAI,OAAO,UAAU,UAAU;AAC3B,UAAM,IAAI,kBAAkB,2CAA2C;AAAA,EAC3E;AACA,cAAY,UAAU,CAAC;AACvB,QAAM,MAAM,QAAQ,WAAW,OAAO,IAAI;AAC1C,QAAM,OAAO,MAAM,MAAM,GAAG,EAAE,GAAG;AACjC,MAAI,OAAO,SAAS,UAAU;AAC1B,UAAM,IAAI,kBAAkB,0CAA0C,MAAM,CAAC,EAAE;AAAA,EACnF;AACA,MAAI;AACJ,MAAI;AACA,cAAU,gBAAgB,IAAI;AAAA,EAClC,SACO,GAAG;AACN,UAAM,IAAI,kBAAkB,qDAAqD,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG;AAAA,EAC7G;AACA,MAAI;AACA,WAAO,KAAK,MAAM,OAAO;AAAA,EAC7B,SACO,GAAG;AACN,UAAM,IAAI,kBAAkB,mDAAmD,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG;AAAA,EAC3G;AACJ;", "names": []}