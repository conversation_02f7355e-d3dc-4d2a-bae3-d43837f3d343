
@import url('https://fonts.googleapis.com/css2?family=Big+Shoulders+Stencil:opsz,wght@10..72,100..900&family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Outfit:wght@100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Quicksand:wght@300..700&family=Sigmar&family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap');


* {
  font-family: "outfit", sans-serif;
}
::-webkit-scrollbar {
  overflow: hidden;
}
.active hr {
  display: block;
}
.top-doctor-container {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}
