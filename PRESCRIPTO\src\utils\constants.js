// API Configuration
export const API_CONFIG = {
  BASE_URL: 'http://localhost:5130/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
};

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  REGISTER: '/register',
  LOGIN: '/login',
  LOGOUT: '/logout',
  VERIFY_OTP: '/verify-otp',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',
  
  // User Management
  GET_CURRENT_USER: '/user',
  GET_USER_BY_ID: '/user',
  DELETE_USER: '/user',
  CHANGE_EMAIL: '/change-email',
  UPDATE_PHONE: '/update-phone',
  VERIFY_PHONE: '/verify-phone',
  
  // Two-Factor Authentication
  SETUP_2FA: '/2fa/setup',
  VERIFY_2FA_SETUP: '/2fa/verify-setup',
  DISABLE_2FA: '/2fa/disable',
  TWO_FACTOR_LOGIN: '/two-factor-login',
  GET_2FA_STATUS: '/2fa/status',
  GENERATE_RECOVERY_CODES: '/2fa/recovery-codes',
  
  // External Authentication
  GOOGLE_AUTH: '/external-auth/google',
  GOOGLE_REGISTER: '/external-auth/google/register',
  
  // Profile Pictures
  UPLOAD_PROFILE_PICTURE: '/profile-picture/upload',
  GET_PROFILE_PICTURE: '/profile-picture',
  DELETE_PROFILE_PICTURE: '/profile-picture',
  
  // Activity & History
  LOGIN_HISTORY: '/login-history',
  ACCOUNT_ACTIVITY: '/account-activity',
};

// Token Configuration
export const TOKEN_CONFIG = {
  ACCESS_TOKEN_KEY: 'accessToken',
  REFRESH_TOKEN_KEY: 'refreshToken',
  TOKEN_EXPIRY_BUFFER: 5 * 60 * 1000, // 5 minutes in milliseconds
};

// User Roles (based on API enum)
export const USER_ROLES = {
  PATIENT: 'Patient',
  DOCTOR: 'Doctor',
  ADMIN: 'Admin',
};

// Gender Options (based on API enum)
export const GENDER_OPTIONS = {
  MALE: 'Male',
  FEMALE: 'Female',
};

// Two-Factor Types
export const TWO_FACTOR_TYPES = {
  AUTHENTICATOR: 'Authenticator',
  SMS: 'SMS',
  EMAIL: 'Email',
};

// Error Codes
export const ERROR_CODES = {
  AUTH_001: 'Invalid credentials',
  AUTH_002: 'Account not verified',
  AUTH_003: 'Account locked',
  VAL_001: 'Validation error',
  CAPT_001: 'CAPTCHA verification failed',
  NETWORK_ERROR: 'Network connection error',
  TIMEOUT_ERROR: 'Request timeout',
  UNKNOWN_ERROR: 'An unexpected error occurred',
};

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  LOCKED: 423,
  INTERNAL_SERVER_ERROR: 500,
};

// Local Storage Keys
export const STORAGE_KEYS = {
  USER_DATA: 'userData',
  REMEMBER_ME: 'rememberMe',
  THEME: 'theme',
  LANGUAGE: 'language',
};

// Route Paths
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  VERIFY_EMAIL: '/verify-email',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',
  TWO_FACTOR_LOGIN: '/two-factor-login',
  DASHBOARD: '/dashboard',
  PROFILE: '/profile',
  ACCOUNT_SETTINGS: '/account-settings',
  SECURITY_SETTINGS: '/security-settings',
  LOGIN_HISTORY: '/login-history',
  ACCOUNT_ACTIVITY: '/account-activity',
  DOCTORS: '/doctors',
  ABOUT: '/about',
  CONTACT: '/contact',
  MY_PROFILE: '/My-Profile',
  MY_APPOINTMENT: '/My-Appointment',
  APPOINTMENT: '/Appointment',
};

// Validation Rules
export const VALIDATION_RULES = {
  PASSWORD: {
    MIN_LENGTH: 12,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_DIGIT: true,
    REQUIRE_SPECIAL_CHAR: true,
    MIN_UNIQUE_CHARS: 4,
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  PHONE: {
    PATTERN: /^\+?[\d\s\-\(\)]+$/,
  },
};

// Toast Configuration
export const TOAST_CONFIG = {
  POSITION: 'top-right',
  AUTO_CLOSE: 5000,
  HIDE_PROGRESS_BAR: false,
  CLOSE_ON_CLICK: true,
  PAUSE_ON_HOVER: true,
  DRAGGABLE: true,
};

// Google OAuth Configuration
export const GOOGLE_CONFIG = {
  CLIENT_ID: process.env.REACT_APP_GOOGLE_CLIENT_ID || '',
  SCOPE: 'openid email profile',
};

// Rate Limiting
export const RATE_LIMITS = {
  LOGIN_ATTEMPTS: 5,
  LOGIN_WINDOW: 30 * 60 * 1000, // 30 minutes
  REGISTRATION_ATTEMPTS: 3,
  REGISTRATION_WINDOW: 60 * 60 * 1000, // 1 hour
  OTP_ATTEMPTS: 3,
  OTP_WINDOW: 15 * 60 * 1000, // 15 minutes
};

// File Upload Configuration
export const FILE_CONFIG = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
};

// Application Metadata
export const APP_CONFIG = {
  NAME: 'PRESCRIPTO',
  VERSION: '1.0.0',
  DESCRIPTION: 'Medical Appointment Booking System',
  AUTHOR: 'PRESCRIPTO Team',
};
